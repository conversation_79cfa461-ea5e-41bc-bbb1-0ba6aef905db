using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace Stylo.Flux
{
    /// <summary>
    /// Helper component to automatically set up LayerOutline system
    /// </summary>
    [System.Serializable]
    public class LayerOutlineSetup : MonoBehaviour
    {
        [Header("Auto Setup")]
        [Tooltip("Automatically add LayerOutlineRendererFeature to URP Renderer")]
        public bool autoAddRendererFeature = true;
        
        [Tooltip("Automatically create LayerOutlineEffect in Global Volume")]
        public bool autoCreateVolumeComponent = true;
        
        [Header("Default Settings")]
        [Tooltip("Default outline intensity")]
        [Range(0f, 2f)]
        public float defaultIntensity = 1f;
        
        [Tooltip("Default outline thickness")]
        [Range(0.1f, 10f)]
        public float defaultThickness = 1f;
        
        [Tooltip("Default outline color")]
        public Color defaultColor = Color.white;
        
        [Header("Layer Selection")]
        [Tooltip("Layers that should have outlines - same as what you'll set in the Volume Component")]
        public LayerMask outlineLayers = -1;

        [Header("Runtime Testing")]
        [Tooltip("Test outline intensity at runtime")]
        [Range(0f, 2f)]
        public float testIntensity = 1f;
        
        private LayerOutlineEffect outlineEffect;

        private void Start()
        {
            if (autoCreateVolumeComponent)
            {
                SetupVolumeComponent();
            }
            
            if (autoAddRendererFeature)
            {
                LogRendererFeatureInstructions();
            }
        }

        private void SetupVolumeComponent()
        {
            // Find or create global volume
            Volume globalVolume = FindFirstObjectByType<Volume>();
            if (globalVolume == null)
            {
                GameObject volumeObject = new GameObject("Global Volume");
                globalVolume = volumeObject.AddComponent<Volume>();
                globalVolume.isGlobal = true;
                
                Debug.Log("[LayerOutlineSetup] Created Global Volume for outline effects");
            }

            // Ensure volume has a profile
            if (globalVolume.profile == null)
            {
                globalVolume.profile = ScriptableObject.CreateInstance<VolumeProfile>();
                Debug.Log("[LayerOutlineSetup] Created VolumeProfile for Global Volume");
            }

            // Add LayerOutlineEffect if not present
            if (!globalVolume.profile.TryGet<LayerOutlineEffect>(out outlineEffect))
            {
                outlineEffect = globalVolume.profile.Add<LayerOutlineEffect>();
                Debug.Log("[LayerOutlineSetup] Added LayerOutlineEffect to Volume Profile");
                
                // Configure default settings
                ConfigureDefaultSettings();
            }
            else
            {
                Debug.Log("[LayerOutlineSetup] LayerOutlineEffect already exists in Volume Profile");
            }
        }

        private void ConfigureDefaultSettings()
        {
            if (outlineEffect == null) return;

            // Master settings
            outlineEffect.enabled = true;
            outlineEffect.intensity = defaultIntensity;
            outlineEffect.thickness = defaultThickness;
            outlineEffect.outlineColor = defaultColor;
            outlineEffect.alpha = 0.8f;

            // Set the layer mask from our configuration
            outlineEffect.outlineLayers = outlineLayers;

            Debug.Log($"[LayerOutlineSetup] Configured outline for layers: {outlineLayers}");
        }



        private void LogRendererFeatureInstructions()
        {
            Debug.Log("[LayerOutlineSetup] To complete setup, add LayerOutlineRendererFeature to your URP Renderer:\n" +
                     "1. Go to your URP Renderer Data asset\n" +
                     "2. Add 'Layer Outline Renderer Feature'\n" +
                     "3. Set Render Pass Event to 'After Rendering Post Processing'\n" +
                     "4. Layer configuration is already done in the Volume Component!");
        }

        private void Update()
        {
            // Runtime testing
            if (outlineEffect != null && Application.isPlaying)
            {
                if (Mathf.Abs(outlineEffect.intensity - testIntensity) > 0.01f)
                {
                    outlineEffect.intensity = testIntensity;
                }
            }
        }

        [ContextMenu("Setup Outline System")]
        public void SetupOutlineSystem()
        {
            SetupVolumeComponent();
            LogRendererFeatureInstructions();
        }

        [ContextMenu("Test Layer Assignment")]
        public void TestLayerAssignment()
        {
            Debug.Log($"Configured Outline Layers: {outlineLayers}");

            // List which specific layers are included
            for (int i = 0; i < 32; i++)
            {
                if ((outlineLayers & (1 << i)) != 0)
                {
                    string layerName = LayerMask.LayerToName(i);
                    Debug.Log($"  - Layer {i}: {layerName}");
                }
            }
        }

        private void OnValidate()
        {
            // Update runtime test intensity
            if (outlineEffect != null && Application.isPlaying)
            {
                outlineEffect.intensity = testIntensity;
            }
        }
    }
}

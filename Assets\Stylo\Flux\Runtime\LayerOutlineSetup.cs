using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace Stylo.Flux
{
    /// <summary>
    /// Helper component to automatically set up LayerOutline system
    /// </summary>
    [System.Serializable]
    public class LayerOutlineSetup : MonoBehaviour
    {
        [Header("Auto Setup")]
        [Tooltip("Automatically add LayerOutlineRendererFeature to URP Renderer")]
        public bool autoAddRendererFeature = true;
        
        [Tooltip("Automatically create LayerOutlineEffect in Global Volume")]
        public bool autoCreateVolumeComponent = true;
        
        [Header("Default Settings")]
        [Tooltip("Default outline intensity")]
        [Range(0f, 2f)]
        public float defaultIntensity = 1f;
        
        [Tooltip("Default outline thickness")]
        [Range(0.1f, 10f)]
        public float defaultThickness = 1f;
        
        [Tooltip("Default outline color")]
        public Color defaultColor = Color.white;
        
        [Header("Layer Assignments")]
        [Tooltip("Layer for player objects")]
        public string playerLayerName = "Player";
        
        [Tooltip("Layer for enemy objects")]
        public string enemyLayerName = "Enemy";
        
        [Toolt<PERSON>("Layer for projectile objects")]
        public string projectileLayerName = "Projectile";
        
        [Tooltip("Layer for ground objects")]
        public string groundLayerName = "Ground";

        [Header("Runtime Testing")]
        [Tooltip("Test outline intensity at runtime")]
        [Range(0f, 2f)]
        public float testIntensity = 1f;
        
        private LayerOutlineEffect outlineEffect;

        private void Start()
        {
            if (autoCreateVolumeComponent)
            {
                SetupVolumeComponent();
            }
            
            if (autoAddRendererFeature)
            {
                LogRendererFeatureInstructions();
            }
        }

        private void SetupVolumeComponent()
        {
            // Find or create global volume
            Volume globalVolume = FindFirstObjectByType<Volume>();
            if (globalVolume == null)
            {
                GameObject volumeObject = new GameObject("Global Volume");
                globalVolume = volumeObject.AddComponent<Volume>();
                globalVolume.isGlobal = true;
                
                Debug.Log("[LayerOutlineSetup] Created Global Volume for outline effects");
            }

            // Ensure volume has a profile
            if (globalVolume.profile == null)
            {
                globalVolume.profile = ScriptableObject.CreateInstance<VolumeProfile>();
                Debug.Log("[LayerOutlineSetup] Created VolumeProfile for Global Volume");
            }

            // Add LayerOutlineEffect if not present
            if (!globalVolume.profile.TryGet<LayerOutlineEffect>(out outlineEffect))
            {
                outlineEffect = globalVolume.profile.Add<LayerOutlineEffect>();
                Debug.Log("[LayerOutlineSetup] Added LayerOutlineEffect to Volume Profile");
                
                // Configure default settings
                ConfigureDefaultSettings();
            }
            else
            {
                Debug.Log("[LayerOutlineSetup] LayerOutlineEffect already exists in Volume Profile");
            }
        }

        private void ConfigureDefaultSettings()
        {
            if (outlineEffect == null) return;

            // Master settings
            outlineEffect.intensity.value = defaultIntensity;
            outlineEffect.intensity.overrideState = true;
            
            outlineEffect.outlineThickness.value = defaultThickness;
            outlineEffect.outlineThickness.overrideState = true;
            
            outlineEffect.defaultOutlineColor.value = defaultColor;
            outlineEffect.defaultOutlineColor.overrideState = true;

            // Configure layer mask based on layer names
            LayerMask layerMask = 0;
            
            int playerLayer = LayerMask.NameToLayer(playerLayerName);
            int enemyLayer = LayerMask.NameToLayer(enemyLayerName);
            int projectileLayer = LayerMask.NameToLayer(projectileLayerName);
            int groundLayer = LayerMask.NameToLayer(groundLayerName);
            
            if (playerLayer != -1) layerMask |= (1 << playerLayer);
            if (enemyLayer != -1) layerMask |= (1 << enemyLayer);
            if (projectileLayer != -1) layerMask |= (1 << projectileLayer);
            if (groundLayer != -1) layerMask |= (1 << groundLayer);
            
            outlineEffect.outlineLayers.value = layerMask;
            outlineEffect.outlineLayers.overrideState = true;



            Debug.Log($"[LayerOutlineSetup] Configured outline for layers: {layerMask}");
        }



        private void LogRendererFeatureInstructions()
        {
            Debug.Log("[LayerOutlineSetup] To complete setup, add LayerOutlineRendererFeature to your URP Renderer:\n" +
                     "1. Go to your URP Renderer Data asset\n" +
                     "2. Add 'Layer Outline Renderer Feature'\n" +
                     "3. Set Render Pass Event to 'After Rendering Post Processing'\n" +
                     "4. Configure layer masks as needed");
        }

        private void Update()
        {
            // Runtime testing
            if (outlineEffect != null && Application.isPlaying)
            {
                if (Mathf.Abs(outlineEffect.intensity.value - testIntensity) > 0.01f)
                {
                    outlineEffect.intensity.value = testIntensity;
                }
            }
        }

        [ContextMenu("Setup Outline System")]
        public void SetupOutlineSystem()
        {
            SetupVolumeComponent();
            LogRendererFeatureInstructions();
        }

        [ContextMenu("Test Layer Assignment")]
        public void TestLayerAssignment()
        {
            Debug.Log($"Player Layer: {LayerMask.NameToLayer(playerLayerName)}");
            Debug.Log($"Enemy Layer: {LayerMask.NameToLayer(enemyLayerName)}");
            Debug.Log($"Projectile Layer: {LayerMask.NameToLayer(projectileLayerName)}");
            Debug.Log($"Ground Layer: {LayerMask.NameToLayer(groundLayerName)}");
        }

        private void OnValidate()
        {
            // Update runtime test intensity
            if (outlineEffect != null && Application.isPlaying)
            {
                outlineEffect.intensity.value = testIntensity;
            }
        }
    }
}

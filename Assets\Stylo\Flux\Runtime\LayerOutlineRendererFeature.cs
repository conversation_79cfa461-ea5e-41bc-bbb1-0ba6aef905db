#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using Stylo.Flux.Universal;

namespace Stylo.Flux
{
    /// <summary>
    /// URP Renderer Feature for layer-based outline effects with Flux integration
    /// </summary>
    public class LayerOutlineRendererFeature : ScriptableRendererFeature
    {
        [System.Serializable]
        public class LayerOutlineSettings
        {
            [Header("Render Settings")]
            public RenderPassEvent renderPassEvent = RenderPassEvent.AfterRenderingPostProcessing;

            [Header("Performance")]
            [Tooltip("Skip outline rendering for scene view cameras")]
            public bool skipSceneView = true;

            [Tooltip("Skip outline rendering for preview cameras")]
            public bool skipPreviewCameras = true;
        }

        [SerializeField] private LayerOutlineSettings settings = new LayerOutlineSettings();
        private LayerOutlineRenderPass outlinePass;
        private Material outlineMaterial;

        public override void Create()
        {
            outlinePass = new LayerOutlineRenderPass(settings);
        }

        public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
        {
            // Skip for unwanted camera types
            if (settings.skipSceneView && renderingData.cameraData.isSceneViewCamera)
                return;
                
#if UNITY_EDITOR
            if (settings.skipPreviewCameras && renderingData.cameraData.isPreviewCamera)
                return;
#endif

            // Ensure we have a valid outline material
            if (!CreateOutlineMaterial())
                return;

            // Check if LayerOutlineEffect volume component is active
            var volumeStack = VolumeManager.instance.stack;
            var outlineEffect = volumeStack.GetComponent<LayerOutlineEffect>();
            
            if (outlineEffect == null || !outlineEffect.IsActive())
                return;

            // Setup and enqueue the render pass
            outlinePass.Setup(outlineMaterial, outlineEffect, settings);
            renderer.EnqueuePass(outlinePass);
        }

        private bool CreateOutlineMaterial()
        {
            if (outlineMaterial != null)
                return true;

            var shader = Shader.Find("Hidden/Stylo/Flux/LayerOutline");
            if (shader == null)
            {
                Debug.LogWarning("[LayerOutlineRendererFeature] LayerOutline shader not found. Please ensure it compiles correctly.");
                return false;
            }

            outlineMaterial = new Material(shader);
            return true;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && outlineMaterial != null)
            {
                CoreUtils.Destroy(outlineMaterial);
                outlineMaterial = null;
            }
        }
    }

    /// <summary>
    /// Render pass for layer-based outline effects
    /// </summary>
    public class LayerOutlineRenderPass : ScriptableRenderPass
    {
        private const string ProfilerTag = "LayerOutline";
        private readonly ProfilingSampler profilingSampler = new ProfilingSampler(ProfilerTag);

        private LayerOutlineRendererFeature.LayerOutlineSettings settings;
        private Material outlineMaterial;
        private LayerOutlineEffect outlineEffect;

        // Render targets
        private RTHandle outlineTexture;
        private RTHandle tempTexture;

        public LayerOutlineRenderPass(LayerOutlineRendererFeature.LayerOutlineSettings settings)
        {
            this.settings = settings;
            renderPassEvent = settings.renderPassEvent;
            
            // Configure input requirements
            ConfigureInput(ScriptableRenderPassInput.Color | ScriptableRenderPassInput.Depth | ScriptableRenderPassInput.Normal);
        }

        public void Setup(Material material, LayerOutlineEffect effect, LayerOutlineRendererFeature.LayerOutlineSettings settings)
        {
            this.outlineMaterial = material;
            this.outlineEffect = effect;
            this.settings = settings;
        }

        public override void OnCameraSetup(CommandBuffer cmd, ref RenderingData renderingData)
        {
            var descriptor = renderingData.cameraData.cameraTargetDescriptor;
            descriptor.depthBufferBits = 0; // No depth buffer needed for outline texture
            descriptor.msaaSamples = 1; // No MSAA for outline processing

            // Create outline texture for intermediate processing
            RenderingUtils.ReAllocateIfNeeded(ref outlineTexture, descriptor, name: "_OutlineTexture");
            RenderingUtils.ReAllocateIfNeeded(ref tempTexture, descriptor, name: "_TempOutlineTexture");
        }

        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
            if (outlineMaterial == null || outlineEffect == null)
                return;

            var cmd = CommandBufferPool.Get(ProfilerTag);
            
            using (new ProfilingScope(cmd, profilingSampler))
            {
                // Get camera color target
                var cameraColorTarget = renderingData.cameraData.renderer.cameraColorTargetHandle;
                
                // Update material properties from volume component
                UpdateMaterialProperties();
                
                // Pass 1: Generate outline
                GenerateOutline(cmd, cameraColorTarget);
                
                // Pass 2: Blend outline with scene
                BlendOutlineWithScene(cmd, cameraColorTarget);
            }

            context.ExecuteCommandBuffer(cmd);
            CommandBufferPool.Release(cmd);
        }

        private void UpdateMaterialProperties()
        {
            // Get Flux effect for integration
            var volumeStack = VolumeManager.instance.stack;
            var fluxEffect = volumeStack.GetComponent<FluxEffect>();
            float fluxIntensity = (fluxEffect != null && fluxEffect.IsActive()) ? fluxEffect.EffectIntensity.value : 0f;

            // Set outline parameters
            outlineMaterial.SetFloat("_OutlineThickness", outlineEffect.thickness);
            outlineMaterial.SetFloat("_DepthThreshold", outlineEffect.depthSensitivity);
            outlineMaterial.SetFloat("_MaxDistance", outlineEffect.maxDistance);
            outlineMaterial.SetFloat("_Intensity", outlineEffect.intensity);

            // Set outline color with alpha
            Color finalColor = outlineEffect.GetFinalOutlineColor();
            outlineMaterial.SetColor("_OutlineColor", finalColor);

            // Enable depth outline mode (simplified)
            outlineMaterial.EnableKeyword("OUTLINE_DEPTH");
        }





        private void GenerateOutline(CommandBuffer cmd, RTHandle cameraColorTarget)
        {
            // Set main texture for edge detection mode
            cmd.SetGlobalTexture("_MainTex", cameraColorTarget);

            // Render outline to intermediate texture
            cmd.SetRenderTarget(outlineTexture);
            cmd.ClearRenderTarget(false, true, Color.clear);
            cmd.DrawMesh(RenderingUtils.fullscreenMesh, Matrix4x4.identity, outlineMaterial, 0, 0); // Pass 0: OutlineGeneration
        }

        private void BlendOutlineWithScene(CommandBuffer cmd, RTHandle cameraColorTarget)
        {
            // Set outline texture for blending
            cmd.SetGlobalTexture("_OutlineTexture", outlineTexture);
            cmd.SetGlobalTexture("_SceneColor", cameraColorTarget);

            // Copy scene to temp texture
            cmd.CopyTexture(cameraColorTarget, tempTexture);
            cmd.SetGlobalTexture("_SceneColor", tempTexture);

            // Blend outline with scene based on selected blend mode
            int blendPass = GetBlendPass();
            cmd.SetRenderTarget(cameraColorTarget);
            cmd.DrawMesh(RenderingUtils.fullscreenMesh, Matrix4x4.identity, outlineMaterial, 0, blendPass);
        }

        private int GetBlendPass()
        {
            // Map blend mode to shader pass
            switch (outlineEffect.fluxBlendMode.value)
            {
                case LayerOutlineEffect.FluxBlendMode.Additive:
                    return 1; // Pass 1: AdditiveBlend
                case LayerOutlineEffect.FluxBlendMode.Multiply:
                    return 2; // Pass 2: MultiplyBlend
                case LayerOutlineEffect.FluxBlendMode.Screen:
                    return 3; // Pass 3: ScreenBlend
                case LayerOutlineEffect.FluxBlendMode.Overlay:
                    return 4; // Pass 4: OverlayBlend
                case LayerOutlineEffect.FluxBlendMode.AdaptiveFlux:
                    return 5; // Pass 5: AdaptiveFluxBlend
                default:
                    return 1; // Default to additive
            }
        }

        public override void OnCameraCleanup(CommandBuffer cmd)
        {
            // Cleanup is handled by RTHandle system
        }

        public void Dispose()
        {
            outlineTexture?.Release();
            tempTexture?.Release();
        }
    }
}

using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace Stylo.Flux
{
    /// <summary>
    /// Simple URP Renderer Feature for outline effects
    /// </summary>
    public class SimpleOutlineRendererFeature : ScriptableRendererFeature
    {
        [System.Serializable]
        public class Settings
        {
            [Header("Render Settings")]
            public RenderPassEvent renderPassEvent = RenderPassEvent.AfterRenderingPostProcessing;
            
            [Header("Performance")]
            public bool skipSceneView = true;
            public bool skipPreviewCameras = true;
        }

        [SerializeField] private Settings settings = new Settings();
        private SimpleOutlineRenderPass outlinePass;
        private Material outlineMaterial;

        public override void Create()
        {
            outlinePass = new SimpleOutlineRenderPass(settings);
        }

        public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
        {
            // Skip for unwanted camera types
            if (settings.skipSceneView && renderingData.cameraData.isSceneViewCamera)
                return;
                
#if UNITY_EDITOR
            if (settings.skipPreviewCameras && renderingData.cameraData.isPreviewCamera)
                return;
#endif

            // Find SimpleOutlineEffect in scene
            var outlineEffect = FindFirstObjectByType<SimpleOutlineEffect>();
            if (outlineEffect == null || !outlineEffect.IsActive())
                return;

            // Ensure we have a valid outline material
            if (!CreateOutlineMaterial())
                return;

            // Setup and enqueue the render pass
            outlinePass.Setup(outlineMaterial, outlineEffect, settings);
            renderer.EnqueuePass(outlinePass);
        }

        private bool CreateOutlineMaterial()
        {
            if (outlineMaterial != null)
                return true;

            var shader = Shader.Find("Hidden/Stylo/Flux/SimpleOutline");
            if (shader == null)
            {
                Debug.LogWarning("[SimpleOutlineRendererFeature] SimpleOutline shader not found. Please ensure it compiles correctly.");
                return false;
            }

            outlineMaterial = new Material(shader);
            return true;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && outlineMaterial != null)
            {
                CoreUtils.Destroy(outlineMaterial);
                outlineMaterial = null;
            }
        }
    }

    /// <summary>
    /// Simple render pass for outline effects
    /// </summary>
    public class SimpleOutlineRenderPass : ScriptableRenderPass
    {
        private const string ProfilerTag = "SimpleOutline";
        private readonly ProfilingSampler profilingSampler = new ProfilingSampler(ProfilerTag);

        private SimpleOutlineRendererFeature.Settings settings;
        private Material outlineMaterial;
        private SimpleOutlineEffect outlineEffect;

        // Render targets
        private RTHandle outlineTexture;

        public SimpleOutlineRenderPass(SimpleOutlineRendererFeature.Settings settings)
        {
            this.settings = settings;
            renderPassEvent = settings.renderPassEvent;
            
            // Configure input requirements
            ConfigureInput(ScriptableRenderPassInput.Color | ScriptableRenderPassInput.Depth);
        }

        public void Setup(Material material, SimpleOutlineEffect effect, SimpleOutlineRendererFeature.Settings settings)
        {
            this.outlineMaterial = material;
            this.outlineEffect = effect;
            this.settings = settings;
        }

        public override void OnCameraSetup(CommandBuffer cmd, ref RenderingData renderingData)
        {
            var descriptor = renderingData.cameraData.cameraTargetDescriptor;
            descriptor.depthBufferBits = 0;
            descriptor.msaaSamples = 1;

            // Create outline texture for processing
            RenderingUtils.ReAllocateIfNeeded(ref outlineTexture, descriptor, name: "_SimpleOutlineTexture");
        }

        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
            if (outlineMaterial == null || outlineEffect == null)
                return;

            var cmd = CommandBufferPool.Get(ProfilerTag);
            
            using (new ProfilingScope(cmd, profilingSampler))
            {
                // Get camera color target
                var cameraColorTarget = renderingData.cameraData.renderer.cameraColorTargetHandle;
                
                // Update material properties
                UpdateMaterialProperties(cameraColorTarget);
                
                // Render outline directly over the scene
                RenderOutline(cmd, cameraColorTarget);
            }

            context.ExecuteCommandBuffer(cmd);
            CommandBufferPool.Release(cmd);
        }

        private void UpdateMaterialProperties(RTHandle cameraColorTarget)
        {
            // Set main texture for edge detection
            outlineMaterial.SetTexture("_MainTex", cameraColorTarget);
            
            // Set outline parameters
            outlineMaterial.SetFloat("_OutlineThickness", outlineEffect.thickness);
            outlineMaterial.SetFloat("_DepthThreshold", outlineEffect.depthSensitivity);
            outlineMaterial.SetFloat("_MaxDistance", outlineEffect.maxDistance);
            outlineMaterial.SetFloat("_Intensity", outlineEffect.intensity);
            
            // Set outline color with alpha
            Color finalColor = outlineEffect.GetFinalOutlineColor();
            outlineMaterial.SetColor("_OutlineColor", finalColor);
        }

        private void RenderOutline(CommandBuffer cmd, RTHandle cameraColorTarget)
        {
            // Render outline directly over the scene using additive blending
            cmd.SetRenderTarget(cameraColorTarget);
            cmd.DrawMesh(RenderingUtils.fullscreenMesh, Matrix4x4.identity, outlineMaterial, 0, 0);
        }

        public override void OnCameraCleanup(CommandBuffer cmd)
        {
            // Cleanup is handled by RTHandle system
        }

        public void Dispose()
        {
            outlineTexture?.Release();
        }
    }
}

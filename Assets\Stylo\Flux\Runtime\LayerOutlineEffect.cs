using UnityEngine;

namespace Stylo.Flux
{
    /// <summary>
    /// Simple outline effect component - no volume system, no override states
    /// </summary>
    public class LayerOutlineEffect : MonoBehaviour
    {
        [System.Serializable]
        public enum OutlineMode
        {
            Depth,
            Normal,
            Edge,
            Stencil
        }

        [System.Serializable]
        public enum FluxBlendMode
        {
            Additive,
            Multiply,
            Screen,
            Overlay,
            AdaptiveFlux
        }



        [Header("🎯 Master Controls")]
        [Tooltip("Master control for overall outline intensity")]
        public ClampedFloatParameter intensity = new ClampedFloatParameter(0f, 0f, 2f);

        [Header("🔍 Outline Detection")]
        [Tooltip("Method used for outline detection")]
        public OutlineModeParameter outlineMode = new OutlineModeParameter(OutlineMode.Depth);
        
        [Tooltip("Base outline thickness")]
        public ClampedFloatParameter outlineThickness = new ClampedFloatParameter(1f, 0.1f, 10f);
        
        [Tooltip("Depth threshold for depth-based outlines")]
        public ClampedFloatParameter depthThreshold = new ClampedFloatParameter(0.1f, 0.001f, 1f);
        
        [Tooltip("Normal threshold for normal-based outlines")]
        public ClampedFloatParameter normalThreshold = new ClampedFloatParameter(0.5f, 0.1f, 2f);
        
        [Tooltip("Maximum distance for outline visibility")]
        public FloatParameter maxDistance = new FloatParameter(50f);

        [Header("🎨 Default Appearance")]
        [Tooltip("Default outline color when layer-specific colors are not used")]
        public ColorParameter defaultOutlineColor = new ColorParameter(Color.white);

        [Header("🌊 Flux Integration")]
        [Tooltip("How outlines blend with Flux effects")]
        public FluxBlendModeParameter fluxBlendMode = new FluxBlendModeParameter(FluxBlendMode.AdaptiveFlux);
        
        [Tooltip("Factor controlling how much Flux intensity affects outline visibility")]
        public ClampedFloatParameter fluxBlendFactor = new ClampedFloatParameter(0.8f, 0f, 1f);
        
        [Tooltip("Curve defining how outline intensity changes with Flux intensity")]
        public AnimationCurveParameter fluxIntensityCurve = new AnimationCurveParameter(
            new AnimationCurve(new Keyframe(0f, 1f), new Keyframe(1f, 0.2f)));

        [Header("📋 Layer Configuration")]
        [Tooltip("Layers that should have outlines - check the layers you want to affect")]
        public LayerMaskParameter outlineLayers = new LayerMaskParameter(-1);

        [Header("🔧 Performance")]
        [Tooltip("Skip outline processing for distant objects")]
        public BoolParameter useDistanceCulling = new BoolParameter(true);
        
        [Tooltip("Distance beyond which outlines are not processed")]
        public FloatParameter cullingDistance = new FloatParameter(100f);

        public bool IsActive() => intensity.value > 0f;

        public bool IsTileCompatible() => false;



        /// <summary>
        /// Calculate effective outline intensity based on Flux integration
        /// </summary>
        public float GetEffectiveIntensity(float fluxIntensity)
        {
            if (fluxBlendMode.value == FluxBlendMode.AdaptiveFlux)
            {
                // Use the curve to determine how outline intensity changes with Flux
                float curveValue = fluxIntensityCurve.value.Evaluate(fluxIntensity);
                return intensity.value * curveValue;
            }
            
            return intensity.value;
        }

        /// <summary>
        /// Check if a specific layer should have outlines
        /// </summary>
        public bool ShouldRenderLayer(int layer)
        {
            return (outlineLayers.value & (1 << layer)) != 0;
        }
    }

    // Custom parameter types for the volume component
    [System.Serializable]
    public sealed class OutlineModeParameter : VolumeParameter<LayerOutlineEffect.OutlineMode>
    {
        public OutlineModeParameter(LayerOutlineEffect.OutlineMode value, bool overrideState = false)
            : base(value, overrideState) { }
    }

    [System.Serializable]
    public sealed class FluxBlendModeParameter : VolumeParameter<LayerOutlineEffect.FluxBlendMode>
    {
        public FluxBlendModeParameter(LayerOutlineEffect.FluxBlendMode value, bool overrideState = false)
            : base(value, overrideState) { }
    }

    [System.Serializable]
    public sealed class LayerMaskParameter : VolumeParameter<LayerMask>
    {
        public LayerMaskParameter(LayerMask value, bool overrideState = false)
            : base(value, overrideState) { }
    }

    [System.Serializable]
    public sealed class AnimationCurveParameter : VolumeParameter<AnimationCurve>
    {
        public AnimationCurveParameter(AnimationCurve value, bool overrideState = false)
            : base(value, overrideState) { }
    }
}

using UnityEngine;

namespace Stylo.Flux
{
    /// <summary>
    /// Simple outline effect component - no volume system, no override states
    /// </summary>
    public class LayerOutlineEffect : MonoBehaviour
    {
        [System.Serializable]
        public enum OutlineMode
        {
            Depth,
            Normal,
            Edge,
            Stencil
        }

        [System.Serializable]
        public enum FluxBlendMode
        {
            Additive,
            Multiply,
            Screen,
            Overlay,
            AdaptiveFlux
        }



        [Header("🎯 Outline Settings")]
        [Tooltip("Enable/disable the outline effect")]
        public bool enabled = false;

        [Tooltip("Outline intensity")]
        [Range(0f, 1f)]
        public float intensity = 0.5f;

        [Tooltip("Outline color")]
        public Color outlineColor = Color.white;

        [Tooltip("Outline thickness")]
        [Range(0.1f, 5f)]
        public float thickness = 1f;

        [Tooltip("Outline transparency")]
        [Range(0f, 1f)]
        public float alpha = 0.8f;

        [Header("📋 Layer Selection")]
        [Tooltip("Layers that should have outlines")]
        public LayerMask outlineLayers = -1;

        [Head<PERSON>("🔧 Detection")]
        [Tooltip("Depth sensitivity for edge detection")]
        [Range(0.001f, 0.5f)]
        public float depthSensitivity = 0.1f;

        [Tooltip("Maximum distance for outlines")]
        public float maxDistance = 50f;

        public bool IsActive() => enabled && intensity > 0f;

        public bool IsTileCompatible() => false;



        /// <summary>
        /// Check if a specific layer should have outlines
        /// </summary>
        public bool ShouldRenderLayer(int layer)
        {
            return (outlineLayers & (1 << layer)) != 0;
        }

        /// <summary>
        /// Get the final outline color with alpha applied
        /// </summary>
        public Color GetFinalOutlineColor()
        {
            Color finalColor = outlineColor;
            finalColor.a = alpha * intensity;
            return finalColor;
        }
    }


}

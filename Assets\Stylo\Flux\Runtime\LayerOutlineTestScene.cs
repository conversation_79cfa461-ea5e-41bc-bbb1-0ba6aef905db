using UnityEngine;
using UnityEngine.Rendering;

namespace Stylo.Flux
{
    /// <summary>
    /// Creates a simple test scene for LayerOutline system
    /// </summary>
    public class LayerOutlineTestScene : MonoBehaviour
    {
        [Header("Test Scene Generation")]
        [Tooltip("Automatically create test objects on start")]
        public bool createTestObjects = true;
        
        [Tooltip("Number of test objects per layer")]
        public int objectsPerLayer = 3;
        
        [Tooltip("Spread distance for test objects")]
        public float spreadDistance = 10f;

        [Header("Layer Names (for creating test objects)")]
        public string playerLayerName = "Player";
        public string enemyLayerName = "Enemy";
        public string projectileLayerName = "Projectile";
        public string groundLayerName = "Ground";

        [Header("Test Materials")]
        public Material playerMaterial;
        public Material enemyMaterial;
        public Material projectileMaterial;
        public Material groundMaterial;

        private void Start()
        {
            if (createTestObjects)
            {
                CreateTestScene();
            }
        }

        [ContextMenu("Create Test Scene")]
        public void CreateTestScene()
        {
            // Create test objects for each layer
            CreateLayerObjects("Player", playerLayerName, playerMaterial, Color.cyan);
            CreateLayerObjects("Enemy", enemyLayerName, enemyMaterial, Color.red);
            CreateLayerObjects("Projectile", projectileLayerName, projectileMaterial, Color.yellow);
            CreateLayerObjects("Ground", groundLayerName, groundMaterial, Color.gray);

            Debug.Log("[LayerOutlineTestScene] Created test scene with objects on different layers");
        }

        private void CreateLayerObjects(string objectType, string layerName, Material material, Color fallbackColor)
        {
            int layer = LayerMask.NameToLayer(layerName);
            if (layer == -1)
            {
                Debug.LogWarning($"[LayerOutlineTestScene] Layer '{layerName}' not found. Please create it in Project Settings > Tags and Layers");
                return;
            }

            for (int i = 0; i < objectsPerLayer; i++)
            {
                // Create object
                GameObject obj = GameObject.CreatePrimitive(GetPrimitiveType(objectType, i));
                obj.name = $"{objectType}_{i + 1}";
                obj.layer = layer;

                // Position objects
                Vector3 position = GetObjectPosition(objectType, i);
                obj.transform.position = position;

                // Scale objects
                Vector3 scale = GetObjectScale(objectType, i);
                obj.transform.localScale = scale;

                // Apply material or create one
                Renderer renderer = obj.GetComponent<Renderer>();
                if (renderer != null)
                {
                    if (material != null)
                    {
                        renderer.material = material;
                    }
                    else
                    {
                        // Create a simple colored material
                        Material newMaterial = new Material(Shader.Find("Universal Render Pipeline/Lit"));
                        newMaterial.color = fallbackColor;
                        renderer.material = newMaterial;
                    }
                }

                // Add some movement for projectiles
                if (objectType == "Projectile")
                {
                    var mover = obj.AddComponent<SimpleProjectileMover>();
                    mover.speed = Random.Range(2f, 5f);
                    mover.direction = Random.insideUnitSphere.normalized;
                }

                // Add rotation for enemies
                if (objectType == "Enemy")
                {
                    var rotator = obj.AddComponent<SimpleRotator>();
                    rotator.rotationSpeed = Random.Range(30f, 90f);
                }
            }
        }

        private PrimitiveType GetPrimitiveType(string objectType, int index)
        {
            switch (objectType)
            {
                case "Player":
                    return PrimitiveType.Capsule;
                case "Enemy":
                    return index % 2 == 0 ? PrimitiveType.Cube : PrimitiveType.Sphere;
                case "Projectile":
                    return PrimitiveType.Sphere;
                case "Ground":
                    return PrimitiveType.Plane;
                default:
                    return PrimitiveType.Cube;
            }
        }

        private Vector3 GetObjectPosition(string objectType, int index)
        {
            Vector3 basePosition = transform.position;
            
            switch (objectType)
            {
                case "Player":
                    return basePosition + new Vector3(-spreadDistance, 1f, index * 3f);
                case "Enemy":
                    return basePosition + new Vector3(spreadDistance, 1f, index * 3f);
                case "Projectile":
                    return basePosition + new Vector3(0f, 3f + index, index * 2f);
                case "Ground":
                    return basePosition + new Vector3(index * 5f, 0f, 0f);
                default:
                    return basePosition + Random.insideUnitSphere * spreadDistance;
            }
        }

        private Vector3 GetObjectScale(string objectType, int index)
        {
            switch (objectType)
            {
                case "Player":
                    return Vector3.one * 1.2f;
                case "Enemy":
                    return Vector3.one * Random.Range(0.8f, 1.5f);
                case "Projectile":
                    return Vector3.one * 0.3f;
                case "Ground":
                    return new Vector3(2f, 1f, 2f);
                default:
                    return Vector3.one;
            }
        }

        [ContextMenu("Clear Test Scene")]
        public void ClearTestScene()
        {
            // Find and destroy test objects
            string[] objectTypes = { "Player", "Enemy", "Projectile", "Ground" };
            
            foreach (string objectType in objectTypes)
            {
                for (int i = 1; i <= objectsPerLayer; i++)
                {
                    GameObject obj = GameObject.Find($"{objectType}_{i}");
                    if (obj != null)
                    {
                        DestroyImmediate(obj);
                    }
                }
            }

            Debug.Log("[LayerOutlineTestScene] Cleared test scene objects");
        }

        [ContextMenu("Test Layer Assignment")]
        public void TestLayerAssignment()
        {
            Debug.Log($"Player Layer: {LayerMask.NameToLayer(playerLayerName)} ({playerLayerName})");
            Debug.Log($"Enemy Layer: {LayerMask.NameToLayer(enemyLayerName)} ({enemyLayerName})");
            Debug.Log($"Projectile Layer: {LayerMask.NameToLayer(projectileLayerName)} ({projectileLayerName})");
            Debug.Log($"Ground Layer: {LayerMask.NameToLayer(groundLayerName)} ({groundLayerName})");
        }
    }

    /// <summary>
    /// Simple component to move projectiles around
    /// </summary>
    public class SimpleProjectileMover : MonoBehaviour
    {
        public float speed = 3f;
        public Vector3 direction = Vector3.forward;
        public float lifetime = 10f;

        private void Start()
        {
            Destroy(gameObject, lifetime);
        }

        private void Update()
        {
            transform.Translate(direction * speed * Time.deltaTime, Space.World);
        }
    }

    /// <summary>
    /// Simple component to rotate objects
    /// </summary>
    public class SimpleRotator : MonoBehaviour
    {
        public float rotationSpeed = 45f;
        public Vector3 rotationAxis = Vector3.up;

        private void Update()
        {
            transform.Rotate(rotationAxis, rotationSpeed * Time.deltaTime, Space.Self);
        }
    }
}

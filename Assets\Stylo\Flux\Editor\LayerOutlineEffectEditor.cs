#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;
using UnityEditor.Rendering;
using Stylo.Flux;

namespace Stylo.Flux.Editor
{
    /// <summary>
    /// Custom editor for LayerOutlineEffect volume component
    /// </summary>
    [CustomEditor(typeof(LayerOutlineEffect))]
    public class LayerOutlineEffectEditor : VolumeComponentEditor
    {
        // UI state
        private bool showLayerSettings = true;
        private bool showFluxIntegration = true;
        private bool showPerformanceSettings = false;

        public override void OnInspectorGUI()
        {
            // Simple default inspector - no complex integration needed
            DrawDefaultInspector();
        }


    }
}
#endif

#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;
using UnityEditor.Rendering;
using Stylo.Flux;

namespace Stylo.Flux.Editor
{
    /// <summary>
    /// Custom editor for LayerOutlineEffect volume component
    /// </summary>
    [CustomEditor(typeof(LayerOutlineEffect))]
    public class LayerOutlineEffectEditor : VolumeComponentEditor
    {
        private SerializedProperty intensity;
        private SerializedProperty outlineMode;
        private SerializedProperty outlineThickness;
        private SerializedProperty depthThreshold;
        private SerializedProperty normalThreshold;
        private SerializedProperty maxDistance;
        private SerializedProperty defaultOutlineColor;
        
        // Flux integration
        private SerializedProperty fluxBlendMode;
        private SerializedProperty fluxBlendFactor;
        private SerializedProperty fluxIntensityCurve;
        
        // Layer configuration
        private SerializedProperty outlineLayers;
        private SerializedProperty playerLayer;
        private SerializedProperty enemyLayer;
        private SerializedProperty projectileLayer;
        private SerializedProperty groundLayer;
        
        // Performance
        private SerializedProperty useDistanceCulling;
        private SerializedProperty cullingDistance;

        // UI state
        private bool showLayerSettings = true;
        private bool showFluxIntegration = true;
        private bool showPerformanceSettings = false;

        public override void OnEnable()
        {
            var o = new PropertyFetcher<LayerOutlineEffect>(serializedObject);

            // Master controls
            intensity = o.Find(x => x.intensity);
            
            // Outline detection
            outlineMode = o.Find(x => x.outlineMode);
            outlineThickness = o.Find(x => x.outlineThickness);
            depthThreshold = o.Find(x => x.depthThreshold);
            normalThreshold = o.Find(x => x.normalThreshold);
            maxDistance = o.Find(x => x.maxDistance);
            defaultOutlineColor = o.Find(x => x.defaultOutlineColor);
            
            // Flux integration
            fluxBlendMode = o.Find(x => x.fluxBlendMode);
            fluxBlendFactor = o.Find(x => x.fluxBlendFactor);
            fluxIntensityCurve = o.Find(x => x.fluxIntensityCurve);
            
            // Layer configuration
            outlineLayers = o.Find(x => x.outlineLayers);
            playerLayer = o.Find(x => x.playerLayer);
            enemyLayer = o.Find(x => x.enemyLayer);
            projectileLayer = o.Find(x => x.projectileLayer);
            groundLayer = o.Find(x => x.groundLayer);
            
            // Performance
            useDistanceCulling = o.Find(x => x.useDistanceCulling);
            cullingDistance = o.Find(x => x.cullingDistance);
        }

        public override void OnInspectorGUI()
        {
            // Header with Flux integration status
            DrawFluxIntegrationStatus();
            
            EditorGUILayout.Space(5);

            // Master Controls
            EditorGUILayout.LabelField("🎯 Master Controls", EditorStyles.boldLabel);
            PropertyField(intensity);
            
            EditorGUILayout.Space(10);

            // Outline Detection
            EditorGUILayout.LabelField("🔍 Outline Detection", EditorStyles.boldLabel);
            PropertyField(outlineMode);
            PropertyField(outlineThickness);
            
            // Show relevant threshold based on outline mode
            var mode = (LayerOutlineEffect.OutlineMode)outlineMode.enumValueIndex;
            switch (mode)
            {
                case LayerOutlineEffect.OutlineMode.Depth:
                    PropertyField(depthThreshold);
                    break;
                case LayerOutlineEffect.OutlineMode.Normal:
                    PropertyField(normalThreshold);
                    break;
            }
            
            PropertyField(maxDistance);
            PropertyField(defaultOutlineColor);
            
            EditorGUILayout.Space(10);

            // Flux Integration
            showFluxIntegration = EditorGUILayout.Foldout(showFluxIntegration, "🌊 Flux Integration", true);
            if (showFluxIntegration)
            {
                EditorGUI.indentLevel++;
                PropertyField(fluxBlendMode);
                PropertyField(fluxBlendFactor);
                
                if (fluxBlendMode.enumValueIndex == (int)LayerOutlineEffect.FluxBlendMode.AdaptiveFlux)
                {
                    PropertyField(fluxIntensityCurve);
                    DrawFluxCurvePreview();
                }
                EditorGUI.indentLevel--;
            }
            
            EditorGUILayout.Space(10);

            // Layer Configuration
            showLayerSettings = EditorGUILayout.Foldout(showLayerSettings, "📋 Layer Configuration", true);
            if (showLayerSettings)
            {
                EditorGUI.indentLevel++;
                PropertyField(outlineLayers);
                
                EditorGUILayout.Space(5);
                DrawLayerSettings("🎮 Player Layer", playerLayer);
                DrawLayerSettings("👹 Enemy Layer", enemyLayer);
                DrawLayerSettings("🚀 Projectile Layer", projectileLayer);
                DrawLayerSettings("🌍 Ground Layer", groundLayer);
                EditorGUI.indentLevel--;
            }
            
            EditorGUILayout.Space(10);

            // Performance Settings
            showPerformanceSettings = EditorGUILayout.Foldout(showPerformanceSettings, "🔧 Performance", true);
            if (showPerformanceSettings)
            {
                EditorGUI.indentLevel++;
                PropertyField(useDistanceCulling);
                if (useDistanceCulling.boolValue)
                {
                    PropertyField(cullingDistance);
                }
                EditorGUI.indentLevel--;
            }
        }

        private void DrawFluxIntegrationStatus()
        {
            // Check if Flux is active in the scene
            var volumeStack = VolumeManager.instance.stack;
            var fluxEffect = volumeStack.GetComponent<Stylo.Flux.Universal.FluxEffect>();
            
            if (fluxEffect != null && fluxEffect.IsActive())
            {
                EditorGUILayout.HelpBox($"✅ Flux Integration Active (Intensity: {fluxEffect.EffectIntensity.value:F2})", MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox("⚠️ Flux Effect not found or inactive. Outline will use default blending.", MessageType.Warning);
            }
        }

        private void DrawLayerSettings(string label, SerializedProperty layerProperty)
        {
            EditorGUILayout.LabelField(label, EditorStyles.boldLabel);
            EditorGUI.indentLevel++;
            
            var enabled = layerProperty.FindPropertyRelative("enabled");
            var color = layerProperty.FindPropertyRelative("color");
            var thicknessMultiplier = layerProperty.FindPropertyRelative("thicknessMultiplier");
            var layerIntensity = layerProperty.FindPropertyRelative("intensity");
            var layerMaxDistance = layerProperty.FindPropertyRelative("maxDistance");
            
            PropertyField(enabled);
            if (enabled.boolValue)
            {
                PropertyField(color);
                PropertyField(thicknessMultiplier);
                PropertyField(layerIntensity);
                PropertyField(layerMaxDistance);
            }
            
            EditorGUI.indentLevel--;
            EditorGUILayout.Space(3);
        }

        private void DrawFluxCurvePreview()
        {
            if (fluxIntensityCurve.animationCurveValue == null) return;
            
            EditorGUILayout.LabelField("Flux Intensity Response Curve", EditorStyles.miniLabel);
            
            var rect = GUILayoutUtility.GetRect(0, 60, GUILayout.ExpandWidth(true));
            rect = EditorGUI.IndentedRect(rect);
            
            // Draw curve preview
            EditorGUI.DrawRect(rect, new Color(0.1f, 0.1f, 0.1f, 0.5f));
            
            var curve = fluxIntensityCurve.animationCurveValue;
            var points = new Vector3[100];
            
            for (int i = 0; i < points.Length; i++)
            {
                float t = i / (float)(points.Length - 1);
                float value = curve.Evaluate(t);
                points[i] = new Vector3(
                    rect.x + t * rect.width,
                    rect.y + rect.height - (value * rect.height),
                    0
                );
            }
            
            // Draw curve line
            Handles.color = Color.cyan;
            Handles.DrawAAPolyLine(2f, points);
            
            // Draw labels
            GUI.Label(new Rect(rect.x, rect.y + rect.height + 2, 100, 20), "Flux: 0%", EditorStyles.miniLabel);
            GUI.Label(new Rect(rect.x + rect.width - 50, rect.y + rect.height + 2, 100, 20), "100%", EditorStyles.miniLabel);
            GUI.Label(new Rect(rect.x - 60, rect.y, 60, 20), "Outline: 100%", EditorStyles.miniLabel);
            GUI.Label(new Rect(rect.x - 40, rect.y + rect.height - 15, 40, 20), "0%", EditorStyles.miniLabel);
        }
    }
}
#endif

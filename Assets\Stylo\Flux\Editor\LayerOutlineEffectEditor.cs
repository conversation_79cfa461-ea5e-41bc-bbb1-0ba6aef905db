#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;
using UnityEditor.Rendering;
using Stylo.Flux;

namespace Stylo.Flux.Editor
{
    /// <summary>
    /// Custom editor for LayerOutlineEffect volume component
    /// </summary>
    [CustomEditor(typeof(LayerOutlineEffect))]
    public class LayerOutlineEffectEditor : VolumeComponentEditor
    {
        // UI state
        private bool showLayerSettings = true;
        private bool showFluxIntegration = true;
        private bool showPerformanceSettings = false;

        public override void OnInspectorGUI()
        {
            var effect = target as LayerOutlineEffect;
            if (effect == null) return;

            // Header with Flux integration status
            DrawFluxIntegrationStatus();

            EditorGUILayout.Space(5);

            // Master Controls
            EditorGUILayout.LabelField("🎯 Master Controls", EditorStyles.boldLabel);
            PropertyField(effect.intensity);

            EditorGUILayout.Space(10);

            // Outline Detection
            EditorGUILayout.LabelField("🔍 Outline Detection", EditorStyles.boldLabel);
            PropertyField(effect.outlineMode);
            PropertyField(effect.outlineThickness);

            // Show relevant threshold based on outline mode
            var mode = effect.outlineMode.value;
            switch (mode)
            {
                case LayerOutlineEffect.OutlineMode.Depth:
                    PropertyField(effect.depthThreshold);
                    break;
                case LayerOutlineEffect.OutlineMode.Normal:
                    PropertyField(effect.normalThreshold);
                    break;
            }

            PropertyField(effect.maxDistance);
            PropertyField(effect.defaultOutlineColor);

            EditorGUILayout.Space(10);

            // Flux Integration
            showFluxIntegration = EditorGUILayout.Foldout(showFluxIntegration, "🌊 Flux Integration", true);
            if (showFluxIntegration)
            {
                EditorGUI.indentLevel++;
                PropertyField(effect.fluxBlendMode);
                PropertyField(effect.fluxBlendFactor);

                if (effect.fluxBlendMode.value == LayerOutlineEffect.FluxBlendMode.AdaptiveFlux)
                {
                    PropertyField(effect.fluxIntensityCurve);
                    DrawFluxCurvePreview(effect.fluxIntensityCurve.value);
                }
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space(10);

            // Layer Configuration
            showLayerSettings = EditorGUILayout.Foldout(showLayerSettings, "📋 Layer Configuration", true);
            if (showLayerSettings)
            {
                EditorGUI.indentLevel++;
                PropertyField(effect.outlineLayers);

                EditorGUILayout.Space(5);
                DrawLayerSettings("🎮 Player Layer", effect.playerLayer);
                DrawLayerSettings("👹 Enemy Layer", effect.enemyLayer);
                DrawLayerSettings("🚀 Projectile Layer", effect.projectileLayer);
                DrawLayerSettings("🌍 Ground Layer", effect.groundLayer);
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space(10);

            // Performance Settings
            showPerformanceSettings = EditorGUILayout.Foldout(showPerformanceSettings, "🔧 Performance", true);
            if (showPerformanceSettings)
            {
                EditorGUI.indentLevel++;
                PropertyField(effect.useDistanceCulling);
                if (effect.useDistanceCulling.value)
                {
                    PropertyField(effect.cullingDistance);
                }
                EditorGUI.indentLevel--;
            }
        }

        private void DrawFluxIntegrationStatus()
        {
            // Check if Flux is active in the scene
            var volumeStack = VolumeManager.instance.stack;
            var fluxEffect = volumeStack.GetComponent<Stylo.Flux.Universal.FluxEffect>();
            
            if (fluxEffect != null && fluxEffect.IsActive())
            {
                EditorGUILayout.HelpBox($"✅ Flux Integration Active (Intensity: {fluxEffect.EffectIntensity.value:F2})", MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox("⚠️ Flux Effect not found or inactive. Outline will use default blending.", MessageType.Warning);
            }
        }

        private void DrawLayerSettings(string label, LayerOutlineEffect.LayerOutlineSettings layerSettings)
        {
            EditorGUILayout.LabelField(label, EditorStyles.boldLabel);
            EditorGUI.indentLevel++;

            PropertyField(layerSettings.enabled);
            if (layerSettings.enabled.value)
            {
                PropertyField(layerSettings.color);
                PropertyField(layerSettings.thicknessMultiplier);
                PropertyField(layerSettings.intensity);
                PropertyField(layerSettings.maxDistance);
            }

            EditorGUI.indentLevel--;
            EditorGUILayout.Space(3);
        }

        private void DrawFluxCurvePreview(AnimationCurve curve)
        {
            if (curve == null) return;

            EditorGUILayout.LabelField("Flux Intensity Response Curve", EditorStyles.miniLabel);

            var rect = GUILayoutUtility.GetRect(0, 60, GUILayout.ExpandWidth(true));
            rect = EditorGUI.IndentedRect(rect);

            // Draw curve preview
            EditorGUI.DrawRect(rect, new Color(0.1f, 0.1f, 0.1f, 0.5f));

            var points = new Vector3[100];

            for (int i = 0; i < points.Length; i++)
            {
                float t = i / (float)(points.Length - 1);
                float value = curve.Evaluate(t);
                points[i] = new Vector3(
                    rect.x + t * rect.width,
                    rect.y + rect.height - (value * rect.height),
                    0
                );
            }

            // Draw curve line
            Handles.color = Color.cyan;
            Handles.DrawAAPolyLine(2f, points);

            // Draw labels
            GUI.Label(new Rect(rect.x, rect.y + rect.height + 2, 100, 20), "Flux: 0%", EditorStyles.miniLabel);
            GUI.Label(new Rect(rect.x + rect.width - 50, rect.y + rect.height + 2, 100, 20), "100%", EditorStyles.miniLabel);
            GUI.Label(new Rect(rect.x - 60, rect.y, 60, 20), "Outline: 100%", EditorStyles.miniLabel);
            GUI.Label(new Rect(rect.x - 40, rect.y + rect.height - 15, 40, 20), "0%", EditorStyles.miniLabel);
        }
    }
}
#endif

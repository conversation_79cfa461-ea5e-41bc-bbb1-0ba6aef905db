# Layer Outline System for Flux

## Overview

The Layer Outline System provides sophisticated edge detection and outline rendering with intelligent Flux integration. It allows you to create different outline effects for different GameObject layers (Player, Enemy, Projectile, Ground, etc.) that automatically adapt to Flux intensity.

## Key Features

- **Layer-Based Control**: Different outline styles per Unity layer
- **Flux Integration**: Outlines automatically adapt to Flux effect intensity
- **Multiple Detection Modes**: Depth, Normal, Edge, and Stencil-based detection
- **Flexible Blending**: Multiple blend modes including adaptive Flux blending
- **Performance Optimized**: Distance culling and efficient rendering

## Quick Setup

### Method 1: Automatic Setup (Recommended)

1. **Add Setup Component**: Add the `LayerOutlineSetup` component to any GameObject in your scene
2. **Configure Settings**: Adjust layer names and default appearance in the inspector
3. **Run Setup**: Click "Setup Outline System" in the context menu or let it run automatically on Start
4. **Add Renderer Feature**: Follow the console instructions to add `LayerOutlineRendererFeature` to your URP Renderer Data

### Method 2: Manual Setup

#### Step 1: Create Unity Layers
1. Go to **Project Settings > Tags and Layers**
2. Create these layers (or use your own names):
   - Player
   - Enemy
   - Projectile
   - Ground

#### Step 2: Add Renderer Feature
1. Open your **URP Renderer Data** asset (usually in Settings folder)
2. Click **"Add Renderer Feature"**
3. Select **"Layer Outline Renderer Feature"**
4. Set **"Render Pass Event"** to **"After Rendering Post Processing"**
5. **Note**: Layer configuration is done in the Volume Component (Step 3), not here

#### Step 3: Add Volume Component (Layer Configuration)
1. Find or create a **Global Volume** in your scene
2. Add **"Layer Outline"** to the Volume Profile
3. Enable the effect by setting **Intensity > 0**
4. Configure **"Outline Layers"** to include your desired layers
5. Set up layer-specific colors, thickness, and intensity

#### Step 4: Assign GameObjects to Layers
1. Select your GameObjects (players, enemies, etc.)
2. Set their **Layer** in the inspector to the appropriate layer
3. Test the outline effect

### Method 3: Test Scene Setup

1. **Add Test Component**: Add `LayerOutlineTestScene` to any GameObject
2. **Create Test Objects**: Click "Create Test Scene" in the context menu
3. **Verify Setup**: Check that outlines appear on the generated test objects
4. **Clean Up**: Use "Clear Test Scene" when done testing

## Configuration

### Master Controls

- **Intensity**: Overall outline effect strength (0-2)
- **Outline Mode**: Detection method (Depth/Normal/Edge/Stencil)
- **Outline Thickness**: Base thickness for all outlines
- **Default Outline Color**: Fallback color when layer-specific colors aren't used

### Outline Detection Modes

#### Depth Mode
- Uses depth buffer differences to detect edges
- **Depth Threshold**: Sensitivity to depth changes (0.001-1.0)
- Best for: General object outlines, architectural elements

#### Normal Mode  
- Uses surface normal differences to detect edges
- **Normal Threshold**: Sensitivity to normal changes (0.1-2.0)
- Best for: Smooth surfaces, character models

#### Edge Mode
- Uses color/luminance differences (Sobel edge detection)
- Best for: High-contrast scenes, stylized visuals

#### Stencil Mode
- Uses stencil buffer values for precise control
- Requires objects to write specific stencil values
- Best for: Precise control, UI elements

### Layer Configuration

Each layer (Player, Enemy, Projectile, Ground) can have:

- **Enabled**: Whether this layer should have outlines
- **Color**: Outline color for this layer
- **Thickness Multiplier**: Scale factor for outline thickness (0-5x)
- **Intensity**: Layer-specific intensity multiplier (0-2x)
- **Max Distance**: Maximum distance for outline visibility

### Flux Integration

#### Blend Modes

1. **Additive**: Outlines add to scene color
2. **Multiply**: Outlines multiply with scene color
3. **Screen**: Screen blend for bright outlines
4. **Overlay**: Overlay blend for contrast
5. **Adaptive Flux**: Intelligent adaptation to Flux intensity

#### Adaptive Flux Mode

- **Flux Blend Factor**: How much Flux affects outline visibility (0-1)
- **Flux Intensity Curve**: Custom curve defining outline response to Flux
  - X-axis: Flux intensity (0-100%)
  - Y-axis: Outline visibility (0-100%)

## Layer Setup

### Unity Layer Configuration

1. Create layers in Project Settings > Tags and Layers:
   - Player
   - Enemy  
   - Projectile
   - Ground

2. Assign GameObjects to appropriate layers

3. Configure LayerOutlineEffect layer masks to include desired layers

### Example Layer Assignments

```csharp
// Player objects - bright cyan outline
playerLayer.enabled = true;
playerLayer.color = Color.cyan;
playerLayer.intensity = 1.2f;

// Enemy objects - red outline  
enemyLayer.enabled = true;
enemyLayer.color = Color.red;
enemyLayer.intensity = 1.5f;

// Projectiles - yellow outline
projectileLayer.enabled = true;
projectileLayer.color = Color.yellow;
projectileLayer.intensity = 0.8f;
```

## Performance Optimization

### Distance Culling
- **Use Distance Culling**: Enable to skip distant outlines
- **Culling Distance**: Maximum distance for outline processing

### Render Pass Optimization
- Runs after post-processing to avoid conflicts
- Shares depth/normal textures with other effects
- Efficient multi-pass rendering

## Flux Integration Examples

### Subtle Adaptation
```
Flux Blend Factor: 0.3
Curve: Linear from 100% to 70%
Result: Outlines slightly fade during intense Flux
```

### Dramatic Adaptation  
```
Flux Blend Factor: 0.8
Curve: Exponential from 100% to 10%
Result: Outlines nearly disappear during Flux
```

### Inverse Adaptation
```
Flux Blend Factor: -0.5
Curve: Inverted from 50% to 150%
Result: Outlines become stronger during Flux
```

## Troubleshooting

### Outlines Not Appearing
1. Check LayerOutlineRendererFeature is added to URP Renderer
2. Verify LayerOutlineEffect intensity > 0
3. Ensure objects are on configured layers
4. Check render pass event order

### Poor Performance
1. Enable distance culling
2. Reduce outline thickness
3. Use depth mode instead of edge mode
4. Limit layer masks to essential objects

### Flux Integration Issues
1. Verify FluxEffect is active in scene
2. Check Flux blend mode settings
3. Adjust Flux intensity curve
4. Test with different blend factors

## Advanced Usage

### Custom Blend Curves
Create custom AnimationCurves for unique Flux responses:
- Stepped curves for threshold effects
- Sine waves for pulsing outlines
- Custom shapes for artistic effects

### Runtime Control
```csharp
var volumeStack = VolumeManager.instance.stack;
var outlineEffect = volumeStack.GetComponent<LayerOutlineEffect>();

// Dynamic intensity control
outlineEffect.intensity.value = Mathf.Sin(Time.time) * 0.5f + 0.5f;

// Layer-specific control
outlineEffect.enemyLayer.color.value = Color.Lerp(Color.red, Color.yellow, threat);
```

### Integration with Other Systems
- Audio-reactive outlines based on music intensity
- Health-based outline colors for characters
- Distance-based outline thickness scaling
- Combat state outline modifications

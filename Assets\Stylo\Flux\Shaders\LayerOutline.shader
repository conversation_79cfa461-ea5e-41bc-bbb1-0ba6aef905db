Shader "Hidden/Stylo/Flux/LayerOutline"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _OutlineColor ("Outline Color", Color) = (1,1,1,1)
        _OutlineThickness ("Outline Thickness", Float) = 1.0
        _DepthThreshold ("Depth Threshold", Float) = 0.1
        _NormalThreshold ("Normal Threshold", Float) = 0.5
        _MaxDistance ("Max Distance", Float) = 50.0
        _Intensity ("Intensity", Float) = 1.0
        _FluxBlendMode ("Flux Blend Mode", Int) = 0
    }

    SubShader
    {
        Tags { "RenderType"="Opaque" "RenderPipeline"="UniversalPipeline" }
        LOD 100
        ZWrite Off
        ZTest Always
        Cull Off

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DeclareDepthTexture.hlsl"
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DeclareNormalsTexture.hlsl"

        // Shader keywords for different outline modes
        #pragma multi_compile _ OUTLINE_DEPTH OUTLINE_NORMAL OUTLINE_STENCIL OUTLINE_EDGE

        struct Attributes
        {
            float4 positionOS : POSITION;
            float2 uv : TEXCOORD0;
        };

        struct Varyings
        {
            float2 uv : TEXCOORD0;
            float4 positionCS : SV_POSITION;
        };

        TEXTURE2D(_MainTex);
        SAMPLER(sampler_MainTex);
        TEXTURE2D(_OutlineTexture);
        SAMPLER(sampler_OutlineTexture);
        TEXTURE2D(_SceneColor);
        SAMPLER(sampler_SceneColor);

        CBUFFER_START(UnityPerMaterial)
            float4 _MainTex_ST;
            float4 _OutlineColor;
            float _OutlineThickness;
            float _DepthThreshold;
            float _NormalThreshold;
            float _MaxDistance;
            float _Intensity;
            int _FluxBlendMode;

            // Flux integration parameters
            float _FluxIntensity;
            float _FluxBlendFactor;

            // Layer-specific parameters (we'll expand this for per-layer support)
            float4 _PlayerLayerColor;
            float4 _EnemyLayerColor;
            float4 _ProjectileLayerColor;
            float4 _GroundLayerColor;

            float _PlayerLayerIntensity;
            float _EnemyLayerIntensity;
            float _ProjectileLayerIntensity;
            float _GroundLayerIntensity;
        CBUFFER_END

        Varyings vert(Attributes input)
        {
            Varyings output;
            output.positionCS = TransformObjectToHClip(input.positionOS.xyz);
            output.uv = TRANSFORM_TEX(input.uv, _MainTex);
            return output;
        }

        // Depth-based outline detection
        float DepthOutline(float2 uv)
        {
            float2 texelSize = _ScreenParams.zw - 1.0;
            float thickness = _OutlineThickness * texelSize.x;

            float depth = SampleSceneDepth(uv);
            float depthN = SampleSceneDepth(uv + float2(0, thickness));
            float depthS = SampleSceneDepth(uv + float2(0, -thickness));
            float depthE = SampleSceneDepth(uv + float2(thickness, 0));
            float depthW = SampleSceneDepth(uv + float2(-thickness, 0));

            float depthDiff = abs(depth - depthN) + abs(depth - depthS) + 
                             abs(depth - depthE) + abs(depth - depthW);

            return step(_DepthThreshold, depthDiff);
        }

        // Normal-based outline detection
        float NormalOutline(float2 uv)
        {
            float2 texelSize = _ScreenParams.zw - 1.0;
            float thickness = _OutlineThickness * texelSize.x;

            float3 normal = SampleSceneNormals(uv);
            float3 normalN = SampleSceneNormals(uv + float2(0, thickness));
            float3 normalS = SampleSceneNormals(uv + float2(0, -thickness));
            float3 normalE = SampleSceneNormals(uv + float2(thickness, 0));
            float3 normalW = SampleSceneNormals(uv + float2(-thickness, 0));

            float normalDiff = distance(normal, normalN) + distance(normal, normalS) + 
                              distance(normal, normalE) + distance(normal, normalW);

            return step(_NormalThreshold, normalDiff);
        }

        // Edge detection outline
        float EdgeOutline(float2 uv)
        {
            float2 texelSize = _ScreenParams.zw - 1.0;
            float thickness = _OutlineThickness * texelSize.x;

            // Sobel edge detection
            float3 color = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, uv).rgb;
            
            float3 colorN = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, uv + float2(0, thickness)).rgb;
            float3 colorS = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, uv + float2(0, -thickness)).rgb;
            float3 colorE = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, uv + float2(thickness, 0)).rgb;
            float3 colorW = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, uv + float2(-thickness, 0)).rgb;

            float3 sobelX = colorE - colorW;
            float3 sobelY = colorN - colorS;
            float edge = length(sobelX) + length(sobelY);

            return saturate(edge * 2.0);
        }

        // Distance fade
        float DistanceFade(float2 uv)
        {
            float depth = SampleSceneDepth(uv);
            float distance = LinearEyeDepth(depth, _ZBufferParams);
            return 1.0 - saturate(distance / _MaxDistance);
        }
        ENDHLSL

        // Pass 0: Outline Generation
        Pass
        {
            Name "OutlineGeneration"
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            float4 frag(Varyings input) : SV_Target
            {
                float outline = 0.0;

                #ifdef OUTLINE_DEPTH
                    outline = DepthOutline(input.uv);
                #elif OUTLINE_NORMAL
                    outline = NormalOutline(input.uv);
                #elif OUTLINE_EDGE
                    outline = EdgeOutline(input.uv);
                #else
                    outline = DepthOutline(input.uv); // Default
                #endif

                // Apply distance fade
                outline *= DistanceFade(input.uv);

                // Apply intensity
                outline *= _Intensity;

                return float4(_OutlineColor.rgb, outline * _OutlineColor.a);
            }
            ENDHLSL
        }

        // Pass 1: Additive Blend
        Pass
        {
            Name "AdditiveBlend"
            Blend One One
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment fragBlend

            float4 fragBlend(Varyings input) : SV_Target
            {
                float4 outline = SAMPLE_TEXTURE2D(_OutlineTexture, sampler_OutlineTexture, input.uv);
                float4 scene = SAMPLE_TEXTURE2D(_SceneColor, sampler_SceneColor, input.uv);
                return scene + outline;
            }
            ENDHLSL
        }

        // Pass 2: Multiply Blend
        Pass
        {
            Name "MultiplyBlend"
            Blend DstColor Zero
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment fragBlend

            float4 fragBlend(Varyings input) : SV_Target
            {
                float4 outline = SAMPLE_TEXTURE2D(_OutlineTexture, sampler_OutlineTexture, input.uv);
                float4 scene = SAMPLE_TEXTURE2D(_SceneColor, sampler_SceneColor, input.uv);
                return lerp(scene, scene * outline, outline.a);
            }
            ENDHLSL
        }

        // Pass 3: Screen Blend
        Pass
        {
            Name "ScreenBlend"
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment fragBlend

            float4 fragBlend(Varyings input) : SV_Target
            {
                float4 outline = SAMPLE_TEXTURE2D(_OutlineTexture, sampler_OutlineTexture, input.uv);
                float4 scene = SAMPLE_TEXTURE2D(_SceneColor, sampler_SceneColor, input.uv);
                float4 screen = 1.0 - (1.0 - scene) * (1.0 - outline);
                return lerp(scene, screen, outline.a);
            }
            ENDHLSL
        }

        // Pass 4: Overlay Blend
        Pass
        {
            Name "OverlayBlend"
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment fragBlend

            float4 fragBlend(Varyings input) : SV_Target
            {
                float4 outline = SAMPLE_TEXTURE2D(_OutlineTexture, sampler_OutlineTexture, input.uv);
                float4 scene = SAMPLE_TEXTURE2D(_SceneColor, sampler_SceneColor, input.uv);
                
                float4 overlay = lerp(2.0 * scene * outline, 
                                     1.0 - 2.0 * (1.0 - scene) * (1.0 - outline),
                                     step(0.5, scene));
                
                return lerp(scene, overlay, outline.a);
            }
            ENDHLSL
        }

        // Pass 5: Adaptive to Flux Blend
        Pass
        {
            Name "AdaptiveFluxBlend"

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment fragBlend

            float4 fragBlend(Varyings input) : SV_Target
            {
                float4 outline = SAMPLE_TEXTURE2D(_OutlineTexture, sampler_OutlineTexture, input.uv);
                float4 scene = SAMPLE_TEXTURE2D(_SceneColor, sampler_SceneColor, input.uv);

                // Use actual Flux intensity from volume component instead of estimation
                float fluxIntensity = _FluxIntensity;

                // Apply Flux blend factor to control how much Flux affects outline visibility
                float adaptiveAlpha = outline.a * (1.0 - saturate(fluxIntensity * _FluxBlendFactor));

                // Smoother blending curve for better visual results
                adaptiveAlpha = smoothstep(0.0, 1.0, adaptiveAlpha);

                return lerp(scene, scene + outline, adaptiveAlpha);
            }
            ENDHLSL
        }
    }
}

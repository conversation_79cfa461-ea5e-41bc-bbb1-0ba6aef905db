using UnityEngine;

namespace Stylo.Flux
{
    /// <summary>
    /// Simple outline effect that overlays on Flux effects
    /// </summary>
    [System.Serializable]
    public class SimpleOutlineEffect : MonoBehaviour
    {
        [Header("🎯 Outline Settings")]
        [Tooltip("Enable/disable the outline effect")]
        public bool enabled = false;
        
        [Tooltip("Outline intensity (0 = off, 1 = full)")]
        [Range(0f, 1f)]
        public float intensity = 0.5f;
        
        [<PERSON>lt<PERSON>("Outline color")]
        public Color outlineColor = Color.white;
        
        [Tooltip("Outline thickness")]
        [Range(0.1f, 5f)]
        public float thickness = 1f;
        
        [Tooltip("Outline transparency")]
        [Range(0f, 1f)]
        public float alpha = 0.8f;

        [Header("📋 Layer Selection")]
        [Tooltip("Layers that should have outlines")]
        public LayerMask outlineLayers = -1;

        [Header("🔧 Detection")]
        [Tooltip("Depth sensitivity for edge detection")]
        [Range(0.001f, 0.5f)]
        public float depthSensitivity = 0.1f;
        
        [Toolt<PERSON>("Maximum distance for outlines")]
        public float maxDistance = 50f;

        /// <summary>
        /// Check if the effect is active
        /// </summary>
        public bool IsActive()
        {
            return enabled && intensity > 0f;
        }

        /// <summary>
        /// Check if a specific layer should have outlines
        /// </summary>
        public bool ShouldRenderLayer(int layer)
        {
            return (outlineLayers & (1 << layer)) != 0;
        }

        /// <summary>
        /// Get the final outline color with alpha applied
        /// </summary>
        public Color GetFinalOutlineColor()
        {
            Color finalColor = outlineColor;
            finalColor.a = alpha * intensity;
            return finalColor;
        }
    }
}

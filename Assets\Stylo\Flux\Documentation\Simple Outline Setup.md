# Simple Outline Effect - Quick Setup

## ✅ What You Get

A simple, working outline effect that:
- ✅ **Overlays on Flux effects** (doesn't interfere)
- ✅ **Visible transparency** - You can see through the outlines
- ✅ **Layer-based** - Choose which layers get outlines
- ✅ **Simple controls** - Color, thickness, transparency, intensity
- ✅ **No complex settings** - Just the essentials

## 🚀 Setup (2 Steps)

### Step 1: Add Renderer Feature
1. Open your **URP Renderer Data** asset
2. Click **"Add Renderer Feature"**
3. Select **"Simple Outline Renderer Feature"**
4. Set **"Render Pass Event"** to **"After Rendering Post Processing"**

### Step 2: Add Component to Scene
1. Add **"Simple Outline Effect"** component to any GameObject in your scene
2. Check **"Enabled"** to turn it on
3. Set **"Intensity"** > 0 to see outlines
4. Choose **"Outline Layers"** - check the layers you want outlined
5. Adjust **color, thickness, transparency** as desired

## 🎯 Simple Controls

### Basic Settings
- **Enabled**: Turn the effect on/off
- **Intensity**: Overall strength (0 = off, 1 = full)
- **Outline Color**: Color of the outlines
- **Thickness**: How thick the outlines are
- **Alpha**: Transparency (0 = invisible, 1 = solid)

### Layer Selection
- **Outline Layers**: Check which layers get outlines (Player, Enemy, etc.)

### Detection
- **Depth Sensitivity**: How sensitive edge detection is
- **Max Distance**: Maximum distance for outlines

## 🧪 Quick Test

1. Create some GameObjects in your scene
2. Put them on different layers (Player, Enemy, etc.)
3. Add the SimpleOutlineEffect component
4. Enable it and set intensity to 0.5
5. Check the layers in "Outline Layers"
6. You should see outlines immediately!

## 🎨 Tips

- **White outlines** work well over Flux effects
- **Lower alpha** (0.3-0.7) gives nice transparency
- **Thickness 1-2** is usually good for most objects
- **Check only important layers** for better performance

## 🔧 Troubleshooting

### No Outlines Visible
1. ✅ Check "Enabled" is checked
2. ✅ Set "Intensity" > 0
3. ✅ Make sure objects are on the selected layers
4. ✅ Verify renderer feature is added to URP Renderer

### Outlines Too Strong/Weak
- Adjust **Intensity** for overall strength
- Adjust **Alpha** for transparency
- Adjust **Depth Sensitivity** for edge detection

This simple system gives you exactly what you need - visible outlines that overlay nicely on Flux effects without complexity!

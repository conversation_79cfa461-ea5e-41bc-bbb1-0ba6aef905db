Shader "Hidden/Stylo/Flux/SimpleOutline"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _OutlineColor ("Outline Color", Color) = (1,1,1,1)
        _OutlineThickness ("Outline Thickness", Float) = 1.0
        _DepthThreshold ("Depth Threshold", Float) = 0.1
        _MaxDistance ("Max Distance", Float) = 50.0
        _Intensity ("Intensity", Float) = 1.0
    }

    SubShader
    {
        Tags { "RenderType"="Transparent" "RenderPipeline"="UniversalPipeline" "Queue"="Transparent" }
        LOD 100
        ZWrite Off
        ZTest Always
        Cull Off
        Blend SrcAlpha OneMinusSrcAlpha

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DeclareDepthTexture.hlsl"

        struct Attributes
        {
            float4 positionOS : POSITION;
            float2 uv : TEXCOORD0;
        };

        struct Varyings
        {
            float2 uv : TEXCOORD0;
            float4 positionCS : SV_POSITION;
        };

        TEXTURE2D(_MainTex);
        SAMPLER(sampler_MainTex);

        CBUFFER_START(UnityPerMaterial)
            float4 _MainTex_ST;
            float4 _OutlineColor;
            float _OutlineThickness;
            float _DepthThreshold;
            float _MaxDistance;
            float _Intensity;
        CBUFFER_END

        Varyings vert(Attributes input)
        {
            Varyings output;
            output.positionCS = TransformObjectToHClip(input.positionOS.xyz);
            output.uv = TRANSFORM_TEX(input.uv, _MainTex);
            return output;
        }

        // Simple depth-based outline detection
        float DepthOutline(float2 uv)
        {
            float2 texelSize = _ScreenParams.zw - 1.0;
            float thickness = _OutlineThickness * texelSize.x;

            float depth = SampleSceneDepth(uv);
            float depthN = SampleSceneDepth(uv + float2(0, thickness));
            float depthS = SampleSceneDepth(uv + float2(0, -thickness));
            float depthE = SampleSceneDepth(uv + float2(thickness, 0));
            float depthW = SampleSceneDepth(uv + float2(-thickness, 0));

            float depthDiff = abs(depth - depthN) + abs(depth - depthS) + 
                             abs(depth - depthE) + abs(depth - depthW);

            return step(_DepthThreshold, depthDiff);
        }

        // Distance fade
        float DistanceFade(float2 uv)
        {
            float depth = SampleSceneDepth(uv);
            float distance = LinearEyeDepth(depth, _ZBufferParams);
            return 1.0 - saturate(distance / _MaxDistance);
        }
        ENDHLSL

        Pass
        {
            Name "SimpleOutline"
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            float4 frag(Varyings input) : SV_Target
            {
                // Generate outline
                float outline = DepthOutline(input.uv);
                
                // Apply distance fade
                outline *= DistanceFade(input.uv);
                
                // Apply intensity
                outline *= _Intensity;
                
                // Return outline color with alpha
                float4 finalColor = _OutlineColor;
                finalColor.a *= outline;
                
                return finalColor;
            }
            ENDHLSL
        }
    }
}
